<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Alternative PDF Navigation Approaches</title>
    <style>
        @media print {
            @page { margin: 0; size: letter; }
            body { margin: 0; padding: 0; }
            .page { page-break-after: always; height: 27.94cm; width: 21.59cm; }
            /* Force links to be visible in PDF */
            a { color: #0066cc !important; text-decoration: underline !important; }
        }
        
        body { margin: 0; font-family: Arial, sans-serif; background: #f0f0f0; }
        .page { width: 21.59cm; height: 27.94cm; background: white; margin: 0 auto 20px; padding: 2cm; box-sizing: border-box; position: relative; }
        .toc-item { margin: 15px 0; padding: 10px; background: #f9f9f9; border-left: 4px solid #0066cc; }
        .page-marker { font-size: 24px; font-weight: bold; color: #cc0000; margin: 20px 0; }
        h1 { color: #333; font-size: 24px; margin-bottom: 20px; }
        h2 { color: #666; font-size: 18px; }
        
        /* Approach 1: Destination elements */
        .destination { 
            position: absolute; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 20px; 
            background: transparent;
        }
        
        /* Approach 2: Target pseudo-class */
        .target-highlight:target {
            background-color: #ffff99 !important;
            border: 2px solid #ff6600 !important;
        }
        
        /* Approach 3: Bookmark-style */
        .bookmark {
            display: block;
            height: 0;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <!-- TOC PAGE -->
    <div class="page">
        <h1>Alternative PDF Navigation Test</h1>
        <p style="background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;">
            <strong>Note:</strong> Since standard anchors didn't work, this tests alternative approaches that some PDF tools support.
        </p>
        
        <h2>Approach 1: Destination Elements</h2>
        <div class="toc-item">
            <a href="#dest1">→ Page 1: Destination Element Method</a>
        </div>
        <div class="toc-item">
            <a href="#dest2">→ Page 2: Destination Element Method</a>
        </div>
        
        <h2>Approach 2: Target Pseudo-class</h2>
        <div class="toc-item">
            <a href="#target1">→ Page 3: Target Highlight Method</a>
        </div>
        <div class="toc-item">
            <a href="#target2">→ Page 4: Target Highlight Method</a>
        </div>
        
        <h2>Approach 3: Bookmark-style Hidden Elements</h2>
        <div class="toc-item">
            <a href="#bookmark1">→ Page 5: Bookmark Method</a>
        </div>
        <div class="toc-item">
            <a href="#bookmark2">→ Page 6: Bookmark Method</a>
        </div>
        
        <h2>Approach 4: Fragment with Query</h2>
        <div class="toc-item">
            <a href="#section-page7">→ Page 7: Fragment Method</a>
        </div>
        <div class="toc-item">
            <a href="#section-page8">→ Page 8: Fragment Method</a>
        </div>
        
        <h2>Approach 5: Data Attributes</h2>
        <div class="toc-item">
            <a href="#data-page9">→ Page 9: Data Attribute Method</a>
        </div>
        <div class="toc-item">
            <a href="#data-page10">→ Page 10: Data Attribute Method</a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb;">
            <h3>Testing Instructions:</h3>
            <ol>
                <li>Test each link in browser first</li>
                <li>Print to PDF</li>
                <li>Test links in PDF</li>
                <li>Note which approach works</li>
            </ol>
        </div>
    </div>
    
    <!-- PAGE 1: Destination Element -->
    <div class="page">
        <div class="destination" id="dest1"></div>
        <div class="page-marker">📍 PAGE 1</div>
        <h1>Page 1: Destination Element Method</h1>
        <h2>Method: &lt;div class="destination" id="dest1"&gt;&lt;/div&gt;</h2>
        <p>This page uses a full-width invisible destination element at the top.</p>
        <p>Some PDF tools work better with block-level destination elements rather than inline anchors.</p>
        <p><strong>Technical details:</strong></p>
        <ul>
            <li>Absolute positioned div</li>
            <li>Full width, 20px height</li>
            <li>Transparent background</li>
            <li>At top of page</li>
        </ul>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
    </div>
    
    <!-- PAGE 2: Destination Element -->
    <div class="page">
        <div class="destination" id="dest2"></div>
        <div class="page-marker">📍 PAGE 2</div>
        <h1>Page 2: Destination Element Method</h1>
        <h2>Method: &lt;div class="destination" id="dest2"&gt;&lt;/div&gt;</h2>
        <p>Second test of the destination element approach.</p>
        <p>If this works, we can implement it in your actual pages.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
    </div>
    
    <!-- PAGE 3: Target Pseudo-class -->
    <div class="page target-highlight" id="target1">
        <div class="page-marker">📍 PAGE 3</div>
        <h1>Page 3: Target Pseudo-class Method</h1>
        <h2>Method: &lt;div class="target-highlight" id="target1"&gt;</h2>
        <p>This page uses CSS :target pseudo-class to highlight when navigated to.</p>
        <p>The entire page should highlight when you click the link.</p>
        <p><strong>CSS used:</strong></p>
        <pre>.target-highlight:target {
    background-color: #ffff99 !important;
    border: 2px solid #ff6600 !important;
}</pre>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
    </div>
    
    <!-- PAGE 4: Target Pseudo-class -->
    <div class="page target-highlight" id="target2">
        <div class="page-marker">📍 PAGE 4</div>
        <h1>Page 4: Target Pseudo-class Method</h1>
        <h2>Method: &lt;div class="target-highlight" id="target2"&gt;</h2>
        <p>Second test of the target pseudo-class approach.</p>
        <p>This method provides visual feedback when navigation works.</p>
        <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
    </div>
    
    <!-- PAGE 5: Bookmark-style -->
    <div class="page">
        <div class="bookmark" id="bookmark1">bookmark1</div>
        <div class="page-marker">📍 PAGE 5</div>
        <h1>Page 5: Bookmark Method</h1>
        <h2>Method: &lt;div class="bookmark" id="bookmark1"&gt;bookmark1&lt;/div&gt;</h2>
        <p>This page uses a hidden bookmark-style element with actual text content.</p>
        <p>Some PDF tools require anchor elements to have text content to work properly.</p>
        <p><strong>CSS used:</strong></p>
        <pre>.bookmark {
    display: block;
    height: 0;
    overflow: hidden;
    margin: 0;
    padding: 0;
}</pre>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
    </div>
    
    <!-- PAGE 6: Bookmark-style -->
    <div class="page">
        <div class="bookmark" id="bookmark2">bookmark2</div>
        <div class="page-marker">📍 PAGE 6</div>
        <h1>Page 6: Bookmark Method</h1>
        <h2>Method: &lt;div class="bookmark" id="bookmark2"&gt;bookmark2&lt;/div&gt;</h2>
        <p>Second test of the bookmark method.</p>
        <p>This approach hides the bookmark text but keeps it accessible to PDF tools.</p>
        <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
    </div>
    
    <!-- PAGE 7: Fragment Method -->
    <div class="page">
        <section id="section-page7">
            <div class="page-marker">📍 PAGE 7</div>
            <h1>Page 7: Fragment Method</h1>
            <h2>Method: &lt;section id="section-page7"&gt;</h2>
            <p>This page uses semantic HTML5 section element as the target.</p>
            <p>Some PDF tools work better with semantic elements like section, article, etc.</p>
            <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores.</p>
        </section>
    </div>
    
    <!-- PAGE 8: Fragment Method -->
    <div class="page">
        <article id="section-page8">
            <div class="page-marker">📍 PAGE 8</div>
            <h1>Page 8: Fragment Method</h1>
            <h2>Method: &lt;article id="section-page8"&gt;</h2>
            <p>This page uses HTML5 article element as the target.</p>
            <p>Testing different semantic elements to see which works best.</p>
            <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        </article>
    </div>
    
    <!-- PAGE 9: Data Attributes -->
    <div class="page" data-page="page9" id="data-page9">
        <div class="page-marker">📍 PAGE 9</div>
        <h1>Page 9: Data Attribute Method</h1>
        <h2>Method: &lt;div data-page="page9" id="data-page9"&gt;</h2>
        <p>This page combines data attributes with ID for enhanced compatibility.</p>
        <p>Some PDF processors look for data attributes to create bookmarks.</p>
        <p><strong>Attributes used:</strong></p>
        <ul>
            <li>data-page="page9"</li>
            <li>id="data-page9"</li>
        </ul>
        <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
    </div>
    
    <!-- PAGE 10: Data Attributes -->
    <div class="page" data-page="page10" id="data-page10">
        <div class="page-marker">📍 PAGE 10</div>
        <h1>Page 10: Data Attribute Method</h1>
        <h2>Method: &lt;div data-page="page10" id="data-page10"&gt;</h2>
        <p>Final test of the data attribute approach.</p>
        <p>This method provides multiple ways for PDF tools to identify page targets.</p>
        <p>Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.</p>
    </div>
    
    <!-- RESULTS PAGE -->
    <div class="page">
        <h1>🔍 Test Results</h1>
        <div style="background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; margin: 20px 0;">
            <h2>Record which methods worked:</h2>
            <p>□ Approach 1: Destination Elements (Pages 1-2)</p>
            <p>□ Approach 2: Target Pseudo-class (Pages 3-4)</p>
            <p>□ Approach 3: Bookmark-style (Pages 5-6)</p>
            <p>□ Approach 4: Fragment Method (Pages 7-8)</p>
            <p>□ Approach 5: Data Attributes (Pages 9-10)</p>
        </div>
        
        <div style="background: #fff3cd; padding: 20px; border: 1px solid #ffeaa7; margin: 20px 0;">
            <h2>⚠️ If NONE of these work:</h2>
            <p>The issue is likely with your PDF generation tool. Consider:</p>
            <ul>
                <li><strong>Try different PDF tools:</strong> Chrome Print, Firefox Print, Puppeteer, Prince XML</li>
                <li><strong>Check PDF tool settings:</strong> Look for "preserve links" or "enable navigation" options</li>
                <li><strong>Use PDF bookmarks instead:</strong> Generate bookmarks programmatically</li>
                <li><strong>Alternative solution:</strong> Create separate TOC PDF and merge with content</li>
            </ul>
        </div>
        
        <div style="background: #d1ecf1; padding: 20px; border: 1px solid #bee5eb;">
            <h2>📋 Next Steps:</h2>
            <p>1. Test this file and report which approaches work</p>
            <p>2. I'll implement the working approach in your actual TOC</p>
            <p>3. If none work, we'll explore PDF tool alternatives</p>
        </div>
    </div>
</body>
</html>
